#!/bin/bash

# Test script for Piper TTS installation
# This script tests if Piper TTS is working correctly with Slovak voices

set -e

echo "🧪 Testing Piper TTS installation..."

# Check if Piper binary exists
if [ ! -f "/usr/local/bin/piper" ]; then
    echo "❌ Piper binary not found at /usr/local/bin/piper"
    echo "Please run install_piper_tts.sh first"
    exit 1
fi

echo "✅ Piper binary found"

# Check Piper version
echo "📋 Piper version:"
/usr/local/bin/piper --version

# Check if voices directory exists
if [ ! -d "/app/voices" ]; then
    echo "❌ Voices directory not found at /app/voices"
    exit 1
fi

echo "✅ Voices directory found"

# List available voices
echo "🎤 Available voices:"
ls -la /app/voices/

# Test Slovak voice
if [ -f "/app/voices/sk_SK-lili-medium.onnx" ]; then
    echo "🧪 Testing Slovak voice..."
    echo "Ahoj, volám sa Lili a som slovenský hlas pre Piper TTS. Toto je test funkčnosti." | /usr/local/bin/piper --model /app/voices/sk_SK-lili-medium.onnx --output_file /tmp/test_slovak_voice.wav
    
    if [ -f "/tmp/test_slovak_voice.wav" ]; then
        echo "✅ Slovak voice test successful!"
        echo "📁 Generated file: /tmp/test_slovak_voice.wav"
        ls -la /tmp/test_slovak_voice.wav
        
        # Check file size (should be > 0)
        size=$(stat -f%z /tmp/test_slovak_voice.wav 2>/dev/null || stat -c%s /tmp/test_slovak_voice.wav 2>/dev/null || echo "0")
        if [ "$size" -gt "1000" ]; then
            echo "✅ Audio file has reasonable size: ${size} bytes"
        else
            echo "⚠️  Audio file seems too small: ${size} bytes"
        fi
    else
        echo "❌ Slovak voice test failed - no output file generated"
        exit 1
    fi
else
    echo "❌ Slovak voice model not found: /app/voices/sk_SK-lili-medium.onnx"
    exit 1
fi

# Test English voice (fallback)
if [ -f "/app/voices/en_US-lessac-medium.onnx" ]; then
    echo "🧪 Testing English voice..."
    echo "Hello, my name is Lessac and I am an English voice for Piper TTS. This is a functionality test." | /usr/local/bin/piper --model /app/voices/en_US-lessac-medium.onnx --output_file /tmp/test_english_voice.wav
    
    if [ -f "/tmp/test_english_voice.wav" ]; then
        echo "✅ English voice test successful!"
        echo "📁 Generated file: /tmp/test_english_voice.wav"
        ls -la /tmp/test_english_voice.wav
    else
        echo "⚠️  English voice test failed - no output file generated"
    fi
else
    echo "⚠️  English voice model not found (optional): /app/voices/en_US-lessac-medium.onnx"
fi

# Test with Node.js environment variables
echo "🔧 Testing environment variables compatibility..."

export PIPER_PATH="/usr/local/bin/piper"
export PIPER_VOICES_PATH="/app/voices"
export TTS_VOICE="sk_SK-lili-medium"

echo "Environment variables set:"
echo "  PIPER_PATH=$PIPER_PATH"
echo "  PIPER_VOICES_PATH=$PIPER_VOICES_PATH"
echo "  TTS_VOICE=$TTS_VOICE"

# Test voice with full path
voice_file="$PIPER_VOICES_PATH/$TTS_VOICE.onnx"
if [ -f "$voice_file" ]; then
    echo "✅ Voice file accessible via environment variables: $voice_file"
    
    # Quick synthesis test
    echo "Toto je test s použitím environment premenných." | $PIPER_PATH --model "$voice_file" --output_file /tmp/test_env_vars.wav
    
    if [ -f "/tmp/test_env_vars.wav" ]; then
        echo "✅ Environment variables test successful!"
        rm -f /tmp/test_env_vars.wav
    else
        echo "❌ Environment variables test failed"
        exit 1
    fi
else
    echo "❌ Voice file not accessible via environment variables: $voice_file"
    exit 1
fi

# Clean up test files
echo "🧹 Cleaning up test files..."
rm -f /tmp/test_slovak_voice.wav /tmp/test_english_voice.wav

echo ""
echo "🎉 All Piper TTS tests passed successfully!"
echo ""
echo "📋 Summary:"
echo "   ✅ Piper binary is working"
echo "   ✅ Slovak voice (Lili) is functional"
echo "   ✅ English voice (Lessac) is functional"
echo "   ✅ Environment variables are correctly configured"
echo ""
echo "🚀 Your Piper TTS installation is ready for production use!"
echo ""
echo "💡 Next steps:"
echo "   1. Make sure your .env file contains:"
echo "      PIPER_PATH=/usr/local/bin/piper"
echo "      PIPER_VOICES_PATH=/app/voices"
echo "      TTS_VOICE=sk_SK-lili-medium"
echo "   2. Restart your Node.js server"
echo "   3. Test voice chat functionality"
