#!/bin/bash

# Piper TTS Installation Script for Oracle VM
# This script installs Piper TTS with Slovak voices

set -e  # Exit on any error

echo "🚀 Starting Piper TTS installation..."

# Check if running as root or with sudo
if [[ $EUID -eq 0 ]]; then
    echo "✅ Running with root privileges"
    SUDO=""
else
    echo "⚠️  Running without root privileges, will use sudo"
    SUDO="sudo"
fi

# Create directories
echo "📁 Creating directories..."
$SUDO mkdir -p /usr/local/bin
$SUDO mkdir -p /app/voices
$SUDO mkdir -p /tmp/piper_install

# Download Piper TTS binary
echo "⬇️  Downloading Piper TTS binary..."
cd /tmp/piper_install

# Get the latest release URL for Linux AMD64
PIPER_VERSION="1.2.0"
PIPER_URL="https://github.com/rhasspy/piper/releases/download/v${PIPER_VERSION}/piper_amd64.tar.gz"

echo "📥 Downloading from: $PIPER_URL"
wget -O piper_amd64.tar.gz "$PIPER_URL"

# Extract and install
echo "📦 Extracting Piper TTS..."
tar -xzf piper_amd64.tar.gz

# Install binary
echo "🔧 Installing Piper binary..."
$SUDO cp piper/piper /usr/local/bin/
$SUDO chmod +x /usr/local/bin/piper

# Verify installation
echo "✅ Verifying Piper installation..."
/usr/local/bin/piper --version

# Download Slovak voices
echo "🎤 Downloading Slovak voices..."

# Slovak female voice - Lili (medium quality)
echo "📥 Downloading Slovak female voice (Lili - medium)..."
cd /tmp/piper_install
wget -O sk_SK-lili-medium.onnx "https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/sk/sk_SK/lili/medium/sk_SK-lili-medium.onnx?download=true"
wget -O sk_SK-lili-medium.onnx.json "https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/sk/sk_SK/lili/medium/sk_SK-lili-medium.onnx.json?download=true"

# Install voices
echo "🔧 Installing Slovak voices..."
$SUDO cp sk_SK-lili-medium.onnx /app/voices/
$SUDO cp sk_SK-lili-medium.onnx.json /app/voices/

# Set proper permissions
echo "🔐 Setting permissions..."
$SUDO chown -R ubuntu:ubuntu /app/voices
$SUDO chmod -R 755 /app/voices

# Test voice synthesis
echo "🧪 Testing Slovak voice synthesis..."
echo "Ahoj, toto je test slovenského hlasu." | /usr/local/bin/piper --model /app/voices/sk_SK-lili-medium.onnx --output_file /tmp/test_slovak.wav

if [ -f "/tmp/test_slovak.wav" ]; then
    echo "✅ Slovak voice test successful! Generated: /tmp/test_slovak.wav"
    ls -la /tmp/test_slovak.wav
else
    echo "❌ Slovak voice test failed!"
    exit 1
fi

# Download additional English voice for fallback
echo "🎤 Downloading English voice for fallback..."
wget -O en_US-lessac-medium.onnx "https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/en/en_US/lessac/medium/en_US-lessac-medium.onnx?download=true"
wget -O en_US-lessac-medium.onnx.json "https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/en/en_US/lessac/medium/en_US-lessac-medium.onnx.json?download=true"

$SUDO cp en_US-lessac-medium.onnx /app/voices/
$SUDO cp en_US-lessac-medium.onnx.json /app/voices/

# Test English voice
echo "🧪 Testing English voice synthesis..."
echo "Hello, this is a test of the English voice." | /usr/local/bin/piper --model /app/voices/en_US-lessac-medium.onnx --output_file /tmp/test_english.wav

if [ -f "/tmp/test_english.wav" ]; then
    echo "✅ English voice test successful! Generated: /tmp/test_english.wav"
    ls -la /tmp/test_english.wav
else
    echo "❌ English voice test failed!"
fi

# Clean up
echo "🧹 Cleaning up temporary files..."
rm -rf /tmp/piper_install
rm -f /tmp/test_slovak.wav /tmp/test_english.wav

# Display installed voices
echo "🎤 Installed voices:"
ls -la /app/voices/

echo ""
echo "🎉 Piper TTS installation completed successfully!"
echo ""
echo "📋 Installation Summary:"
echo "   • Piper binary: /usr/local/bin/piper"
echo "   • Voices directory: /app/voices"
echo "   • Slovak voice: sk_SK-lili-medium.onnx"
echo "   • English voice: en_US-lessac-medium.onnx"
echo ""
echo "🔧 Environment variables for your .env file:"
echo "   PIPER_PATH=/usr/local/bin/piper"
echo "   PIPER_VOICES_PATH=/app/voices"
echo "   TTS_VOICE=sk_SK-lili-medium"
echo ""
echo "🚀 You can now restart your Node.js server to use Piper TTS!"
