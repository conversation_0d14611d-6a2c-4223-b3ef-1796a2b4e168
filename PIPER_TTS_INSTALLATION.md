# Piper TTS Installation Guide

Tento návod vás prevedie inštaláciou Piper TTS na Oracle VM so slovenskými hlasmi.

## 🚀 <PERSON><PERSON><PERSON><PERSON> inštalácia

### 1. Pripojenie na Oracle VM

```bash
ssh ubuntu@*************
```

### 2. Stiah<PERSON>ie inštalačn<PERSON>ch skriptov

```bash
# Stiahnutie skriptov z vášho projektu
wget https://raw.githubusercontent.com/your-repo/install_piper_tts.sh
wget https://raw.githubusercontent.com/your-repo/test_piper_tts.sh

# Alebo ak máte súbory lokálne, použite scp:
scp install_piper_tts.sh ubuntu@*************:~/
scp test_piper_tts.sh ubuntu@*************:~/
```

### 3. Spustenie inštalácie

```bash
# Nastavenie práv na spustenie
chmod +x install_piper_tts.sh
chmod +x test_piper_tts.sh

# Spustenie inštalácie
./install_piper_tts.sh
```

### 4. Testovanie inštalácie

```bash
# Test funkčnosti
./test_piper_tts.sh
```

## 📋 Čo sa nainštaluje

### Piper TTS Binary
- **Umiestnenie**: `/usr/local/bin/piper`
- **Verzia**: 1.2.0
- **Architektúra**: Linux AMD64

### Slovenské hlasy
- **sk_SK-lili-medium.onnx** - Slovenský ženský hlas (Lili)
- **sk_SK-lili-medium.onnx.json** - Konfiguračný súbor

### Anglické hlasy (fallback)
- **en_US-lessac-medium.onnx** - Americký anglický hlas (Lessac)
- **en_US-lessac-medium.onnx.json** - Konfiguračný súbor

### Adresárová štruktúra
```
/usr/local/bin/piper          # Piper TTS binary
/app/voices/                  # Hlasy
├── sk_SK-lili-medium.onnx
├── sk_SK-lili-medium.onnx.json
├── en_US-lessac-medium.onnx
└── en_US-lessac-medium.onnx.json
```

## 🔧 Konfigurácia

### Environment premenné v .env súbore

Po úspešnej inštalácii aktualizujte váš `.env` súbor:

```bash
# TTS Configuration
PIPER_PATH=/usr/local/bin/piper
PIPER_VOICES_PATH=/app/voices
TTS_VOICE=sk_SK-lili-medium
TTS_CACHE_ENABLED=true
```

### Reštart servera

```bash
# V adresári chat-backend
pm2 restart voice-chat
# alebo
npm run start
```

## 🧪 Testovanie

### Manuálny test Piper TTS

```bash
# Test slovenského hlasu
echo "Ahoj, toto je test slovenského hlasu." | /usr/local/bin/piper --model /app/voices/sk_SK-lili-medium.onnx --output_file test.wav

# Test anglického hlasu
echo "Hello, this is a test of English voice." | /usr/local/bin/piper --model /app/voices/en_US-lessac-medium.onnx --output_file test_en.wav
```

### Test cez Node.js API

```bash
# Test TTS endpointu
curl -X POST http://*************:3000/api/tts/synthesize \
  -H "Content-Type: application/json" \
  -d '{"text": "Ahoj, toto je test TTS API.", "voice": "sk_SK-lili-medium"}' \
  --output test_api.wav
```

## 🔍 Riešenie problémov

### Piper binary sa nenašiel
```bash
# Skontrolujte inštaláciu
which piper
ls -la /usr/local/bin/piper

# Ak neexistuje, spustite inštaláciu znovu
./install_piper_tts.sh
```

### Hlasy sa nenašli
```bash
# Skontrolujte hlasy
ls -la /app/voices/

# Skontrolujte práva
sudo chown -R ubuntu:ubuntu /app/voices
sudo chmod -R 755 /app/voices
```

### Server hlási chyby
```bash
# Skontrolujte logy
pm2 logs voice-chat

# Skontrolujte environment premenné
cat /path/to/your/.env | grep -E "(PIPER|TTS)"
```

### Test hlasu zlyhá
```bash
# Spustite test skript
./test_piper_tts.sh

# Manuálny test s debug výstupom
echo "test" | /usr/local/bin/piper --model /app/voices/sk_SK-lili-medium.onnx --output_file /tmp/debug.wav --debug
```

## 📊 Výkon a kvalita

### Slovenský hlas (Lili - medium)
- **Kvalita**: Stredná (vhodná pre produkciu)
- **Rýchlosť**: ~2-3x rýchlejšie ako real-time
- **Veľkosť modelu**: ~63MB
- **Podporované znaky**: Slovenská abeceda, interpunkcia

### Anglický hlas (Lessac - medium)
- **Kvalita**: Stredná (fallback pre anglické texty)
- **Rýchlosť**: ~2-3x rýchlejšie ako real-time
- **Veľkosť modelu**: ~63MB

## 🔄 Aktualizácia

### Aktualizácia Piper TTS
```bash
# Stiahnutie novej verzie
wget https://github.com/rhasspy/piper/releases/download/v1.2.0/piper_amd64.tar.gz
tar -xzf piper_amd64.tar.gz
sudo cp piper/piper /usr/local/bin/
```

### Pridanie nových hlasov
```bash
# Príklad: pridanie českého hlasu
cd /tmp
wget "https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/cs/cs_CZ/jirka/medium/cs_CZ-jirka-medium.onnx?download=true" -O cs_CZ-jirka-medium.onnx
wget "https://huggingface.co/rhasspy/piper-voices/resolve/v1.0.0/cs/cs_CZ/jirka/medium/cs_CZ-jirka-medium.onnx.json?download=true" -O cs_CZ-jirka-medium.onnx.json

sudo cp cs_CZ-jirka-medium.* /app/voices/
```

## ✅ Overenie úspešnej inštalácie

Po dokončení inštalácie by ste mali vidieť:

1. ✅ Piper binary funguje: `/usr/local/bin/piper --version`
2. ✅ Slovenský hlas je dostupný: `ls /app/voices/sk_SK-lili-medium.*`
3. ✅ Test syntézy funguje: `./test_piper_tts.sh`
4. ✅ Server používa Piper namiesto mock TTS
5. ✅ Hlasový chat generuje skutočný zvuk

## 🎉 Hotovo!

Vaša inštalácia Piper TTS je kompletná. Teraz môžete používať hlasový chat s kvalitným slovenským hlasom!
