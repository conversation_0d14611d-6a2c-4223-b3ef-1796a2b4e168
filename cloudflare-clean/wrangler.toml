name = "oracle-voice-chat"
compatibility_date = "2024-07-25"

# Pages configuration
pages_build_output_dir = "."

# Build configuration removed - handled by deploy script

# Environment variables for production
[vars]
ORACLE_BACKEND = "https://*************"
WS_URL = "wss://*************"

# Environment variables for preview deployments
[env.preview.vars]
ORACLE_BACKEND = "https://*************"
WS_URL = "wss://*************"
