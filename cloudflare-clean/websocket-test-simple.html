<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebSocket Test - Oracle Voice Chat</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            font-weight: bold;
        }
        .status.connecting { background: #fff3cd; color: #856404; }
        .status.connected { background: #d4edda; color: #155724; }
        .status.disconnected { background: #f8d7da; color: #721c24; }
        .status.error { background: #f8d7da; color: #721c24; }
        .log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 15px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        input[type="text"] {
            width: 300px;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔌 WebSocket Test - Oracle Voice Chat</h1>
        
        <div id="status" class="status disconnected">
            ❌ Disconnected
        </div>
        
        <div>
            <button id="connectBtn" onclick="connect()">Connect</button>
            <button id="disconnectBtn" onclick="disconnect()" disabled>Disconnect</button>
            <button onclick="clearLog()">Clear Log</button>
        </div>
        
        <div>
            <input type="text" id="messageInput" placeholder="Enter message..." disabled>
            <button id="sendBtn" onclick="sendMessage()" disabled>Send</button>
        </div>
        
        <div id="log" class="log"></div>
    </div>

    <script>
        let ws = null;
        let sessionId = null;
        
        const statusEl = document.getElementById('status');
        const logEl = document.getElementById('log');
        const connectBtn = document.getElementById('connectBtn');
        const disconnectBtn = document.getElementById('disconnectBtn');
        const sendBtn = document.getElementById('sendBtn');
        const messageInput = document.getElementById('messageInput');
        
        // WebSocket URL - using environment variable or fallback
        const WS_URL = window.WS_URL || 'wss://*************/ws';
        
        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.innerHTML = `<span style="color: #666">[${timestamp}]</span> ${message}`;
            if (type === 'error') logEntry.style.color = '#dc3545';
            if (type === 'success') logEntry.style.color = '#28a745';
            if (type === 'warning') logEntry.style.color = '#ffc107';
            logEl.appendChild(logEntry);
            logEl.scrollTop = logEl.scrollHeight;
        }
        
        function updateStatus(status, message) {
            statusEl.className = `status ${status}`;
            statusEl.innerHTML = message;
        }
        
        function connect() {
            if (ws && ws.readyState === WebSocket.OPEN) {
                log('Already connected!', 'warning');
                return;
            }
            
            log(`🔌 Connecting to ${WS_URL}...`);
            updateStatus('connecting', '🔄 Connecting...');
            
            try {
                ws = new WebSocket(WS_URL);
                
                ws.onopen = function(event) {
                    log('✅ WebSocket connection opened!', 'success');
                    updateStatus('connected', '✅ Connected');
                    
                    connectBtn.disabled = true;
                    disconnectBtn.disabled = false;
                    sendBtn.disabled = false;
                    messageInput.disabled = false;
                };
                
                ws.onmessage = function(event) {
                    try {
                        const data = JSON.parse(event.data);
                        log(`📨 Received: ${JSON.stringify(data, null, 2)}`, 'success');
                        
                        if (data.type === 'connection' && data.sessionId) {
                            sessionId = data.sessionId;
                            log(`🆔 Session ID: ${sessionId}`, 'info');
                        }
                    } catch (e) {
                        log(`📨 Received (raw): ${event.data}`, 'success');
                    }
                };
                
                ws.onclose = function(event) {
                    log(`🔌 WebSocket connection closed. Code: ${event.code}, Reason: ${event.reason}`, 'warning');
                    updateStatus('disconnected', '❌ Disconnected');
                    
                    connectBtn.disabled = false;
                    disconnectBtn.disabled = true;
                    sendBtn.disabled = true;
                    messageInput.disabled = true;
                    
                    if (event.code === 1006) {
                        log('⚠️  Code 1006 indicates abnormal closure - connection was terminated unexpectedly', 'error');
                    }
                };
                
                ws.onerror = function(error) {
                    log(`❌ WebSocket error: ${error}`, 'error');
                    updateStatus('error', '❌ Error');
                };
                
            } catch (error) {
                log(`❌ Failed to create WebSocket: ${error.message}`, 'error');
                updateStatus('error', '❌ Connection Failed');
            }
        }
        
        function disconnect() {
            if (ws) {
                log('🔌 Disconnecting...');
                ws.close(1000, 'User requested disconnect');
            }
        }
        
        function sendMessage() {
            const message = messageInput.value.trim();
            if (!message) return;
            
            if (ws && ws.readyState === WebSocket.OPEN) {
                const payload = {
                    type: 'test',
                    message: message,
                    timestamp: new Date().toISOString(),
                    sessionId: sessionId
                };
                
                ws.send(JSON.stringify(payload));
                log(`📤 Sent: ${JSON.stringify(payload, null, 2)}`);
                messageInput.value = '';
            } else {
                log('❌ WebSocket is not connected!', 'error');
            }
        }
        
        function clearLog() {
            logEl.innerHTML = '';
        }
        
        // Allow Enter key to send message
        messageInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                sendMessage();
            }
        });
        
        // Initial log
        log('🚀 WebSocket Test Page Loaded');
        log(`🔗 Target URL: ${WS_URL}`);
        log('👆 Click "Connect" to start testing');
    </script>
</body>
</html>
