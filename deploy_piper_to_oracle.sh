#!/bin/bash

# Deployment script for Piper TTS to Oracle VM
# This script copies files to Oracle VM and runs the installation

set -e

ORACLE_HOST="*************"
ORACLE_USER="ubuntu"

echo "🚀 Deploying Piper TTS to Oracle VM..."

# Check if SSH key is available
if [ ! -f ~/.ssh/id_rsa ] && [ ! -f ~/.ssh/slavosmn ]; then
    echo "❌ No SSH key found. Please ensure you have SSH access to Oracle VM."
    echo "Available keys:"
    ls -la ~/.ssh/
    exit 1
fi

# Try different SSH keys
SSH_KEY=""
if [ -f ~/.ssh/slavosmn ]; then
    SSH_KEY="-i ~/.ssh/slavosmn"
elif [ -f ~/.ssh/id_rsa ]; then
    SSH_KEY="-i ~/.ssh/id_rsa"
fi

echo "🔑 Using SSH key: $SSH_KEY"

# Test SSH connection
echo "🔍 Testing SSH connection..."
if ! ssh $SSH_KEY -o ConnectTimeout=10 -o StrictHostKeyChecking=no $ORACLE_USER@$ORACLE_HOST "echo 'SSH connection successful'"; then
    echo "❌ SSH connection failed. Please check:"
    echo "   1. SSH key is correct"
    echo "   2. Oracle VM is running"
    echo "   3. Security groups allow SSH (port 22)"
    exit 1
fi

echo "✅ SSH connection successful"

# Copy installation scripts
echo "📁 Copying installation scripts..."
scp $SSH_KEY install_piper_tts.sh $ORACLE_USER@$ORACLE_HOST:~/
scp $SSH_KEY test_piper_tts.sh $ORACLE_USER@$ORACLE_HOST:~/

# Make scripts executable and run installation
echo "🔧 Running Piper TTS installation on Oracle VM..."
ssh $SSH_KEY $ORACLE_USER@$ORACLE_HOST << 'EOF'
    echo "🏁 Starting installation on Oracle VM..."
    
    # Make scripts executable
    chmod +x install_piper_tts.sh
    chmod +x test_piper_tts.sh
    
    # Run installation
    echo "📦 Installing Piper TTS..."
    ./install_piper_tts.sh
    
    # Run tests
    echo "🧪 Testing installation..."
    ./test_piper_tts.sh
    
    echo "✅ Piper TTS installation completed on Oracle VM!"
EOF

# Update .env file on Oracle VM
echo "🔧 Updating .env file..."
ssh $SSH_KEY $ORACLE_USER@$ORACLE_HOST << 'EOF'
    # Backup current .env
    if [ -f /home/<USER>/chat-backend/.env ]; then
        cp /home/<USER>/chat-backend/.env /home/<USER>/chat-backend/.env.backup.$(date +%Y%m%d_%H%M%S)
        echo "📋 Backed up existing .env file"
    fi
    
    # Update .env with Piper TTS settings
    cd /home/<USER>/chat-backend
    
    # Remove old TTS settings if they exist
    sed -i '/^PIPER_PATH=/d' .env 2>/dev/null || true
    sed -i '/^PIPER_VOICES_PATH=/d' .env 2>/dev/null || true
    sed -i '/^TTS_VOICE=/d' .env 2>/dev/null || true
    
    # Add new TTS settings
    echo "" >> .env
    echo "# Piper TTS Configuration" >> .env
    echo "PIPER_PATH=/usr/local/bin/piper" >> .env
    echo "PIPER_VOICES_PATH=/app/voices" >> .env
    echo "TTS_VOICE=sk_SK-lili-medium" >> .env
    
    echo "✅ Updated .env file with Piper TTS configuration"
    
    # Show relevant .env settings
    echo "📋 Current TTS settings in .env:"
    grep -E "(PIPER|TTS)" .env || echo "No TTS settings found"
EOF

# Restart the Node.js server
echo "🔄 Restarting Node.js server..."
ssh $SSH_KEY $ORACLE_USER@$ORACLE_HOST << 'EOF'
    cd /home/<USER>/chat-backend
    
    # Check if PM2 is running the app
    if pm2 list | grep -q "voice-chat"; then
        echo "🔄 Restarting with PM2..."
        pm2 restart voice-chat
        pm2 logs voice-chat --lines 10
    else
        echo "⚠️  PM2 process 'voice-chat' not found. You may need to start the server manually:"
        echo "   cd /home/<USER>/chat-backend"
        echo "   npm start"
    fi
EOF

# Final verification
echo "🔍 Final verification..."
ssh $SSH_KEY $ORACLE_USER@$ORACLE_HOST << 'EOF'
    echo "🧪 Final tests..."
    
    # Test Piper binary
    if /usr/local/bin/piper --version; then
        echo "✅ Piper binary is working"
    else
        echo "❌ Piper binary test failed"
    fi
    
    # Test Slovak voice
    if [ -f "/app/voices/sk_SK-lili-medium.onnx" ]; then
        echo "✅ Slovak voice is installed"
    else
        echo "❌ Slovak voice not found"
    fi
    
    # Test server status
    if curl -s http://localhost:3000/api/tts/status | grep -q "operational\|mock"; then
        echo "✅ TTS API is responding"
    else
        echo "⚠️  TTS API test inconclusive"
    fi
EOF

echo ""
echo "🎉 Piper TTS deployment completed!"
echo ""
echo "📋 Summary:"
echo "   ✅ Piper TTS installed on Oracle VM"
echo "   ✅ Slovak voice (Lili) available"
echo "   ✅ .env file updated"
echo "   ✅ Server restarted"
echo ""
echo "🔗 Next steps:"
echo "   1. Test voice chat at: https://291965a7.oracle-voice-chat.pages.dev"
echo "   2. Check server logs: ssh $ORACLE_USER@$ORACLE_HOST 'pm2 logs voice-chat'"
echo "   3. Monitor TTS status: curl http://$ORACLE_HOST:3000/api/tts/status"
echo ""
echo "🎤 Your voice chat should now use real Slovak TTS instead of mock audio!"
